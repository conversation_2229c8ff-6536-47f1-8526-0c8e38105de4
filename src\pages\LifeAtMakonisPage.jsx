import React, { useState, useEffect, useCallback, useRef } from "react";
import useScrollToTop from "../hooks/useScrollToTop";

// Image and music data (unchanged, but ensured paths are correct)
const imageData = [
  { src: "/assets/1.jpg", alt: "Makonis team working in the office" },
  { src: "/assets/3.jpg", alt: "Makonis office space" },
  { src: "/assets/4.jpg", alt: "Team collaboration at Makonis" },
  { src: "/assets/5.jpg", alt: "A celebratory moment at Makonis" },
  { src: "/assets/6.jpg", alt: "Makonis team event" },
  { src: "/assets/7.jpg", alt: "A project discussion" },
  { src: "/assets/8.jpg", alt: "Office amenities" },
  { src: "/assets/9.jpg", alt: "Team building activity" },
  { src: "/assets/11.jpg", alt: "Casual Friday at Makonis" },
  { src: "/assets/13.jpg", alt: "Team bonding activity" },
  { src: "/assets/14.jpg", alt: "Team bonding activity" },
  { src: "/assets/15.jpg", alt: "Makonis team working in the office" },
  { src: "/assets/16.jpg", alt: "Makonis office space" },
  { src: "/assets/18.jpg", alt: "Team collaboration at Makonis" },
  { src: "/assets/19.jpg", alt: "A celebratory moment at Makonis" },
  { src: "/assets/20.jpg", alt: "Makonis team event" },
  { src: "/assets/21.jpg", alt: "A project discussion" },
  { src: "/assets/22.jpg", alt: "Office amenities" },
  { src: "/assets/23.jpg", alt: "Team building activity" },
  { src: "/assets/25.jpg", alt: "Team bonding activity" },
  { src: "/assets/26.jpg", alt: "Team bonding activity" },
  { src: "/assets/28.jpg", alt: "Team bonding activity" },
  { src: "/assets/29.jpg", alt: "Team bonding activity" },
  { src: "/assets/30.jpeg", alt: "Team bonding activity" },
  { src: "/assets/31.jpeg", alt: "Team bonding activity" },
  { src: "/assets/32.jpeg", alt: "Team bonding activity" },
  { src: "/assets/33.png", alt: "Team bonding activity" },
  { src: "/assets/34.jpeg", alt: "Team bonding activity" },
];

const musicData = [
  { src: "/music/1.mp3", title: "Inspiring Cinematic" },
  { src: "/music/2.mp3", title: "Uplifting Corporate" },
  { src: "/music/3.mp3", title: "Peaceful Background" },
];

const LifeAtMakonisPage = () => {
  useScrollToTop();
  const [selectedImage, setSelectedImage] = useState(null);
  const [isMemoriesPlayerOpen, setIsMemoriesPlayerOpen] = useState(false);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [nextVideoIndex, setNextVideoIndex] = useState(1); // For cross-fade
  const [currentMusic, setCurrentMusic] = useState(null);
  const [isPlaying, setIsPlaying] = useState(true); // For audio controls
  const [isLoading, setIsLoading] = useState(false); // Preload indicator
  const audioRef = useRef(null);
  const videoIntervalRef = useRef(null);

  // Preload all images for smooth slideshow
  useEffect(() => {
    const preloadImages = () => {
      setIsLoading(true);
      const promises = imageData.map((img) => {
        return new Promise((resolve, reject) => {
          const image = new Image();
          image.src = img.src;
          image.onload = resolve;
          image.onerror = reject;
        });
      });
      Promise.all(promises)
        .then(() => setIsLoading(false))
        .catch((err) => console.error("Image preload failed:", err));
    };
    preloadImages();
  }, []);

  const handleNext = useCallback(() => {
    if (selectedImage !== null) {
      const currentIndex = imageData.findIndex((img) => img.src === selectedImage);
      const nextIndex = (currentIndex + 1) % imageData.length;
      setSelectedImage(imageData[nextIndex].src);
    }
  }, [selectedImage]);

  const handlePrev = useCallback(() => {
    if (selectedImage !== null) {
      const currentIndex = imageData.findIndex((img) => img.src === selectedImage);
      const prevIndex = (currentIndex - 1 + imageData.length) % imageData.length;
      setSelectedImage(imageData[prevIndex].src);
    }
  }, [selectedImage]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!selectedImage) return;
      if (event.key === "ArrowRight") handleNext();
      if (event.key === "ArrowLeft") handlePrev();
      if (event.key === "Escape") setSelectedImage(null);
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedImage, handleNext, handlePrev]);

  const handlePlayMemories = () => {
    if (isLoading) return; // Wait for preload
    const randomTrack = musicData[Math.floor(Math.random() * musicData.length)];
    setCurrentMusic(randomTrack);
    setCurrentVideoIndex(0);
    setNextVideoIndex(1);
    setIsMemoriesPlayerOpen(true);
    setIsPlaying(true);
  };

  const handleCloseMemories = () => {
    setIsMemoriesPlayerOpen(false);
    if (audioRef.current) audioRef.current.pause();
    clearInterval(videoIntervalRef.current);
  };

  const togglePlayPause = () => {
    if (isPlaying) {
      audioRef.current?.pause();
      clearInterval(videoIntervalRef.current);
    } else {
      audioRef.current?.play().catch((e) => console.error("Audio play failed", e));
      startSlideshowInterval();
    }
    setIsPlaying(!isPlaying);
  };

  const startSlideshowInterval = () => {
    clearInterval(videoIntervalRef.current);
    videoIntervalRef.current = setInterval(() => {
      setCurrentVideoIndex((prev) => {
        const next = (prev + 1) % imageData.length;
        setNextVideoIndex((next + 1) % imageData.length);
        return next;
      });
    }, 4000); // Matches animation duration
  };

  useEffect(() => {
    if (isMemoriesPlayerOpen && !isLoading) {
      audioRef.current?.play().catch((e) => console.error("Audio play failed", e));
      startSlideshowInterval();
    }
    return () => clearInterval(videoIntervalRef.current);
  }, [isMemoriesPlayerOpen, isLoading]);

  const accentRgb = "0, 160, 233";
  const backgroundColor = "#001a35";

  return (
    <>
      <style>{`
        @keyframes kenburns {
          0% { transform: scale(1) translate(0, 0); opacity: 0; }
          10% { opacity: 1; }
          90% { transform: scale(1.15) translate(-2%, 2%); opacity: 1; }
          100% { opacity: 0; }
        }
        .animate-kenburns { animation: kenburns 4s ease-in-out forwards; }
        .cross-fade { transition: opacity 0.5s ease-in-out; }
        .fade-in { animation: fadeIn 1s ease-in; }
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        /* Progress bar */
        .progress-bar { height: 4px; background: #00a0e9; transition: width 4s linear; }
      `}</style>

      <div
        style={{
          backgroundColor: backgroundColor,
          backgroundImage: `radial-gradient(circle at 30% 100%, rgba(${accentRgb}, 0.15) 0%, transparent 40%), radial-gradient(circle at 90% 20%, rgba(${accentRgb}, 0.1) 0%, transparent 30%)`,
        }}
      >
        {/* Enhanced Header Section: Added fade-in animation */}
        <div className="relative h-screen w-full flex items-center justify-center overflow-hidden">
          <div
            className="absolute inset-0 bg-cover bg-center filter blur-sm fade-in"
            style={{
              backgroundImage: "url('https://images.unsplash.com/photo-1519389950473-47ba0277781c?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c29mdHdhcmUlMjBkZXZlbG9wbWVudCUyMGNvbXBhbnl8ZW58MHx8MHx8fDA%3D')",
            }}
          ></div>
          <div className="absolute inset-0 bg-black opacity-50"></div>
          <div className="relative z-10 text-center px-4 fade-in" style={{ animationDelay: "0.5s" }}>
            <h2
              style={{
                fontSize: "clamp(2.5rem, 8vw, 4rem)",
                fontWeight: "800",
                letterSpacing: "2.6px",
                marginBottom: "1rem",
                background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
              }}
            >
              Life @ Makonis
            </h2>
          </div>
        </div>

        {/* Memories Section: Added loading state to button */}
        <div className="text-center py-20 px-4">
          <h3 className="text-3xl sm:text-4xl font-bold text-white mb-4">Our Shared Moments</h3>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto mb-8">
            Take a look back at the journey, collaborations, and celebrations that define the vibrant culture at Makonis.
          </p>
          <button
            onClick={handlePlayMemories}
            disabled={isLoading}
            className="inline-flex items-center gap-x-3 px-8 py-4 bg-[#00a0e9] text-white font-semibold rounded-lg shadow-lg hover:bg-[#0088cc] focus:outline-none focus:ring-2 focus:ring-[#00a0e9] focus:ring-offset-2 focus:ring-offset-[#001a35] transition-all duration-300 transform hover:scale-105 disabled:opacity-50"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{isLoading ? "Loading..." : "Play Memories"}</span>
          </button>
        </div>

        {/* Enhanced Grid Section: Lazy loading, captions on hover, masonry-like grid */}
        <div className="mx-auto max-w-7xl px-4 pb-16 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 auto-rows-fr">
            {imageData.map((image, index) => (
              <div
                key={`${image.src}-${index}`}
                className="group relative overflow-hidden rounded-xl shadow-lg cursor-pointer"
                onClick={() => setSelectedImage(image.src)}
              >
                <img
                  src={image.src}
                  alt={image.alt}
                  loading="lazy" // Lazy loading for performance
                  onError={(e) => { e.target.src = "/assets/fallback.jpg"; }} // Fallback image (add a placeholder in /public/assets)
                  className="w-full h-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <p className="text-white text-center px-4">{image.alt}</p> {/* Hover caption */}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Enhanced Full-screen Image Modal (Lightbox): Added caption and zoom */}
        {selectedImage && (
          <div
            className="fixed inset-0 bg-black bg-opacity-90 z-[9999] flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div className="relative max-w-full max-h-full flex flex-col items-center">
              <img
                src={selectedImage}
                alt="Full screen view"
                className="max-w-full max-h-[80vh] object-contain transition-transform duration-300 hover:scale-110" // Simple zoom on hover
                onClick={(e) => e.stopPropagation()}
              />
              <p className="text-white mt-4">{imageData.find((img) => img.src === selectedImage)?.alt}</p> {/* Caption */}
            </div>
            <button onClick={() => setSelectedImage(null)} className="absolute top-5 right-5 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
            </button>
            <button onClick={(e) => { e.stopPropagation(); handlePrev(); }} className="absolute left-5 top-1/2 -translate-y-1/2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" /></svg>
            </button>
            <button onClick={(e) => { e.stopPropagation(); handleNext(); }} className="absolute right-5 top-1/2 -translate-y-1/2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" /></svg>
            </button>
          </div>
        )}

        {/* Enhanced Memories Video Player Modal: Cross-fade, progress bar, controls, caption */}
        {isMemoriesPlayerOpen && (
          <div className="fixed inset-0 bg-black z-[9999] flex flex-col items-center justify-center">
            <div className="absolute inset-0 overflow-hidden">
              {/* Current image */}
              <img
                key={`current-${currentVideoIndex}`}
                src={imageData[currentVideoIndex].src}
                alt={imageData[currentVideoIndex].alt}
                className="w-full h-full object-cover animate-kenburns cross-fade"
                style={{ opacity: 1 }}
              />
              {/* Next image for cross-fade */}
              <img
                key={`next-${nextVideoIndex}`}
                src={imageData[nextVideoIndex].src}
                alt={imageData[nextVideoIndex].alt}
                className="w-full h-full object-cover absolute top-0 left-0 animate-kenburns cross-fade"
                style={{ opacity: 0, animationDelay: "3.5s" }} // Fades in near end
              />
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            </div>

            <div className="relative z-10 w-full h-full flex flex-col justify-between p-6">
              <div className="flex justify-between items-start">
                <div className="bg-black bg-opacity-50 text-white p-3 rounded-lg">
                  <p className="font-semibold text-lg">Life @ Makonis: Our Story</p>
                  {currentMusic && <p className="text-sm opacity-80">🎵 Now Playing: {currentMusic.title}</p>}
                </div>
                <button onClick={handleCloseMemories} className="text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
              </div>
              <div className="text-white text-center bg-black bg-opacity-50 p-4 rounded-lg self-center max-w-lg">
                <p className="font-medium mb-2">{imageData[currentVideoIndex].alt}</p> {/* Image caption */}
                <button onClick={togglePlayPause} className="mx-4 text-white">
                  {isPlaying ? "Pause" : "Play"}
                </button>
                <div className="text-sm mt-2">{currentVideoIndex + 1} / {imageData.length}</div>
              </div>
              {/* Progress bar */}
              <div className="w-full h-1 bg-gray-700">
                <div className="progress-bar" style={{ width: isPlaying ? "100%" : "0%", animationPlayState: isPlaying ? "running" : "paused" }}></div>
              </div>
            </div>

            <audio ref={audioRef} src={currentMusic?.src} loop />
          </div>
        )}
      </div>
    </>
  );
};

export default LifeAtMakonisPage;
