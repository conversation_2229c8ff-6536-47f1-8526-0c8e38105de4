import React, { useState, useEffect, useCallback, useRef } from "react";
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import useScrollToTop from "../hooks/useScrollToTop";

// import images (unchanged)
import img1 from "../assets/1.jpg";
import img3 from "../assets/3.jpg";
import img4 from "../assets/4.jpg";
import img5 from "../assets/5.jpg";
import img6 from "../assets/6.jpg";
import img7 from "../assets/7.jpg";
import img8 from "../assets/8.jpg";
import img9 from "../assets/9.jpg";
import img11 from "../assets/11.jpg";
import img13 from "../assets/13.jpg";
import img14 from "../assets/14.jpg";
import img15 from "../assets/15.jpg";
import img16 from "../assets/16.jpg";
import img18 from "../assets/18.jpg";
import img19 from "../assets/19.jpg";
import img20 from "../assets/20.jpg";
import img21 from "../assets/21.jpg";
import img22 from "../assets/22.jpg";
import img23 from "../assets/23.jpg";
import img25 from "../assets/25.jpg";
import img26 from "../assets/26.jpg";
import img28 from "../assets/28.jpg";
import img29 from "../assets/29.jpg";
import img30 from "../assets/30.jpeg";
import img31 from "../assets/31.jpeg";
import img32 from "../assets/32.jpeg";
import img33 from "../assets/33.png";
import img34 from "../assets/34.jpeg";
// music
import music1 from "../music/background-music-soft-peaceful-336346.mp3";
import music2 from "../music/peaceful-piano-background-music-218762.mp3";
import music3 from "../music/peaceful-piano-dreams-312614.mp3";

gsap.registerPlugin(ScrollTrigger);

const imageData = [
  { src: img1, alt: "Makonis team working in the office" },
  { src: img3, alt: "Makonis office space" },
  { src: img4, alt: "Team collaboration at Makonis" },
  { src: img5, alt: "A celebratory moment at Makonis" },
  { src: img6, alt: "Makonis team event" },
  { src: img7, alt: "A project discussion" },
  { src: img8, alt: "Office amenities" },
  { src: img9, alt: "Team building activity" },
  { src: img11, alt: "Casual Friday at Makonis" },
  { src: img13, alt: "Team bonding activity" },
  { src: img14, alt: "Team bonding activity" },
  { src: img15, alt: "Makonis team working in the office" },
  { src: img16, alt: "Makonis office space" },
  { src: img18, alt: "Team collaboration at Makonis" },
  { src: img19, alt: "A celebratory moment at Makonis" },
  { src: img20, alt: "Makonis team event" },
  { src: img21, alt: "A project discussion" },
  { src: img22, alt: "Office amenities" },
  { src: img23, alt: "Team building activity" },
  { src: img25, alt: "Team bonding activity" },
  { src: img26, alt: "Team bonding activity" },
  { src: img28, alt: "Team bonding activity" },
  { src: img29, alt: "Team bonding activity" },
  { src: img30, alt: "Team bonding activity" },
  { src: img31, alt: "Team bonding activity" },
  { src: img32, alt: "Team bonding activity" },
  { src: img33, alt: "Team bonding activity" },
  { src: img34, alt: "Team bonding activity" },
];

const musicData = [
  { src: music1, title: "Soft Peaceful Background" },
  { src: music2, title: "Peaceful Piano Background" },
  { src: music3, title: "Peaceful Piano Dreams" },
];

const LifeAtMakonisPage = () => {
  useScrollToTop();
  const [selectedImage, setSelectedImage] = useState(null);
  const [isMemoriesPlayerOpen, setIsMemoriesPlayerOpen] = useState(false);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [currentMusic, setCurrentMusic] = useState(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [imagesPerPage] = useState(12);
  const audioRef = useRef(null);
  const videoIntervalRef = useRef(null);
  // Refs for GSAP and scrollLock
  const sectionRef = useRef(null);
  const heroRef = useRef(null);
  const memoriesRef = useRef(null);
  const galleryRef = useRef(null);
  const cardsRef = useRef([]);
  const backgroundRef = useRef(null);
  const originalStyles = useRef({ body: {}, html: {} });

  // Prevent pagination from blanking out the whole gallery
  const totalPages = Math.ceil(imageData.length / imagesPerPage);
  useEffect(() => {
    if (currentPage < 1) setCurrentPage(1);
    if (currentPage > totalPages) setCurrentPage(totalPages || 1);
  }, [currentPage, totalPages]);

  const startIndex = (currentPage - 1) * imagesPerPage;
  const endIndex = startIndex + imagesPerPage;
  const currentImages = imageData.slice(startIndex, endIndex);

  const lockScroll = () => {
    const scrollY = window.scrollY;
    originalStyles.current.body = {
      overflow: document.body.style.overflow,
      position: document.body.style.position,
      width: document.body.style.width,
      top: document.body.style.top,
    };
    originalStyles.current.html = {
      overflow: document.documentElement.style.overflow,
    };
    document.body.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.width = '100%';
    document.body.style.top = `-${scrollY}px`;
    document.documentElement.style.overflow = 'hidden';
  };
  const unlockScroll = () => {
    try {
      const scrollY = document.body.style.top;

      // Reset all styles
      document.body.style.overflow = originalStyles.current.body.overflow || '';
      document.body.style.position = originalStyles.current.body.position || '';
      document.body.style.width = originalStyles.current.body.width || '';
      document.body.style.top = originalStyles.current.body.top || '';
      document.documentElement.style.overflow = originalStyles.current.html.overflow || '';

      // Force reset to ensure scroll is restored
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.top = '';
      document.documentElement.style.overflow = '';

      // Restore scroll position
      if (scrollY) {
        const scrollPosition = parseInt(scrollY.replace('px', '')) * -1;
        window.scrollTo(0, scrollPosition);
      }

      console.log('Scroll unlocked successfully');
    } catch (error) {
      console.error('Error unlocking scroll:', error);
      // Fallback: force reset everything
      document.body.style.cssText = '';
      document.documentElement.style.overflow = '';
    }
  };

  // Preload images for smoothness; show loading if not ready
  useEffect(() => {
    setIsLoading(true);
    Promise.all(
      imageData.map(img =>
        new Promise(resolve => {
          const image = new window.Image();
          image.src = img.src;
          image.onload = resolve;
          image.onerror = resolve;
        })
      )
    ).then(() => setIsLoading(false));
  }, []);
  
  const handleNext = useCallback(() => {
    if (selectedImage !== null) {
      const currentIndex = imageData.findIndex((img) => img.src === selectedImage);
      const nextIndex = (currentIndex + 1) % imageData.length;
      setSelectedImage(imageData[nextIndex].src);
    }
  }, [selectedImage]);
  const handlePrev = useCallback(() => {
    if (selectedImage !== null) {
      const currentIndex = imageData.findIndex((img) => img.src === selectedImage);
      const prevIndex = (currentIndex - 1 + imageData.length) % imageData.length;
      setSelectedImage(imageData[prevIndex].src);
    }
  }, [selectedImage]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape") {
        if (selectedImage) {
          setSelectedImage(null);
          unlockScroll();
        }
        if (isMemoriesPlayerOpen) {
          handleCloseMemories();
        }
      }
      if (!selectedImage) return;
      if (event.key === "ArrowRight") handleNext();
      if (event.key === "ArrowLeft") handlePrev();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedImage, isMemoriesPlayerOpen, handleNext, handlePrev]);

  const handlePlayMemories = () => {
    if (isLoading) return;
    const randomTrack = musicData[Math.floor(Math.random() * musicData.length)];
    setCurrentMusic(randomTrack);
    setCurrentVideoIndex(0);
    setIsMemoriesPlayerOpen(true);
    setIsPlaying(true);
    lockScroll();
  };

  const handleCloseMemories = () => {
    setIsMemoriesPlayerOpen(false);
    audioRef.current?.pause();
    clearInterval(videoIntervalRef.current);
    unlockScroll();
  };

  const togglePlayPause = () => {
    if (isPlaying) {
      audioRef.current?.pause();
      clearInterval(videoIntervalRef.current);
    } else {
      audioRef.current?.play().catch(() => {});
      startSlideshowInterval();
    }
    setIsPlaying(!isPlaying);
  };
  const startSlideshowInterval = () => {
    clearInterval(videoIntervalRef.current);
    videoIntervalRef.current = setInterval(() => {
      setCurrentVideoIndex(prev => (prev + 1) % imageData.length);
    }, 5000);
  };
  useEffect(() => {
    if (isMemoriesPlayerOpen && !isLoading) {
      audioRef.current?.play().catch(() => {});
      startSlideshowInterval();
    }
    return () => clearInterval(videoIntervalRef.current);
    // eslint-disable-next-line
  }, [isMemoriesPlayerOpen, isLoading]);

  // GSAP Animations (unchanged)
  useEffect(() => {
    const ctx = gsap.context(() => {
      // ...original gsap animations here as in your code...
    }, sectionRef);
    return () => ctx.revert();
  }, []);

  // On unmount: always restore scroll
  useEffect(() => () => unlockScroll(), []);

  // Enhanced escape key handler
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        if (isMemoriesPlayerOpen) {
          handleCloseMemories();
        }
        if (selectedImage) {
          setSelectedImage(null);
          unlockScroll();
        }
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isMemoriesPlayerOpen, selectedImage]);

  // Pagination controls - now clamps, cannot hit an empty page
  const handlePageChange = (page) => {
    if (page < 1) page = 1;
    if (page > totalPages) page = totalPages;
    setCurrentPage(page);
    if (galleryRef.current) {
      galleryRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // TEMP DEBUG UI
  // Uncomment to show in UI:
  // console.log({currentPage, totalPages, imageDataLength: imageData.length, currentImagesLen: currentImages.length});

  return (
    <>
    <style>{`
      @keyframes kenburns {0%{transform:scale(1) translate(0,0);opacity:1;}25%{transform:scale(1.02) translate(-0.5%,0.5%);opacity:1;}50%{transform:scale(1.03) translate(0.5%,-0.5%);opacity:1;}75%{transform:scale(1.02) translate(-0.5%,0.5%);opacity:1;}100%{transform:scale(1) translate(0,0);opacity:1;}}
      @keyframes fadeInOut {0%{opacity:0;}10%{opacity:1;}90%{opacity:1;}100%{opacity:0;}}
      .animate-kenburns {animation: kenburns 8s ease-in-out infinite;}
      .no-zoom {animation: none !important; transform: none !important;}
      .animate-fade-in-out {animation: fadeInOut 5s ease-in-out forwards;}
      .cross-fade {transition: opacity 1s ease-in-out;}
      .fade-in {opacity:1;animation:fadeIn 1s ease-in;}
      @keyframes fadeIn {from{opacity:0;}to{opacity:1;}}
      .progress-bar{height:4px;background:linear-gradient(90deg,#00a0e9,#0088cc);transition:width 5s linear;border-radius:2px;}
    `}</style>

    <div ref={sectionRef} className="min-h-screen relative overflow-hidden" style={{
      background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
      backdropFilter: "blur(10px)",
    }}>
      {/* HEADER & HERO ...unchanged... */}
      <div ref={heroRef} className="relative h-screen w-full flex items-center justify-center overflow-hidden z-10">
        {/* ...unchanged hero JSX here... */}
        <div className="relative z-20 text-center px-4">
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight mb-8"
            style={{
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}>
            Life @ Makonis
          </h1>
          {/* ...rest unchanged */}
        </div>
      </div>

      {/* Memories / Play Memories Button */}
      <div ref={memoriesRef} className="relative z-20 text-center py-20 px-4" style={{ backgroundColor: 'rgba(0, 41, 86, 0.8)' }}>
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight mb-4"
          style={{
            background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            backgroundClip: "text",
          }}>
          Our Shared Moments
        </h2>
        <p className="text-base sm:text-lg text-white/80 max-w-2xl mx-auto mb-8">
          Take a look back at the journey, collaborations, and celebrations that define the vibrant culture at Makonis.
        </p>
        <div className="flex flex-col items-center gap-4">
          <button
            onClick={handlePlayMemories}
            disabled={isLoading}
            className="inline-flex items-center gap-x-3 px-12 py-6 text-white font-bold text-xl rounded-2xl shadow-2xl focus:outline-none transition-all duration-300 transform hover:scale-105 disabled:opacity-50 border-2 border-[#00a0e9] hover:border-[#00a0e9] z-50 relative"
            style={{
              background: "#00a0e9",
              boxShadow: "0 0 40px rgba(0, 160, 233, 0.6), 0 10px 30px rgba(0, 0, 0, 0.3)",
              minHeight: "60px",
              minWidth: "200px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
            <span>{isLoading ? "Loading..." : "Play Memories"}</span>
          </button>
          <div style={{ color: 'white', fontSize: '12px', marginTop: '10px' }}>
            Debug: isLoading={isLoading.toString()}, Images loaded: {imageData.length}, Music tracks: {musicData.length}
          </div>
        </div>
      </div>

      {/* Gallery Cards */}
      <div ref={galleryRef} className="relative z-10 mx-auto max-w-7xl px-4 pb-16 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-in">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight mb-4 animate-in"
            style={{
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
            }}>
            Our Journey in Pictures
          </h2>
          <p className="text-base sm:text-lg text-white/80 max-w-2xl mx-auto animate-in">
            Explore the moments that define our culture, teamwork, and shared experiences at Makonis.
          </p>
        </div>
        {!currentImages.length && (
          <div style={{color:'#fff', background:'#002956', border:'1px solid #00a0e9', padding:'2em', borderRadius:'1em'}}>No images to display for page {currentPage}. Try going to another page.</div>
        )}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {currentImages.map((image, index) => (
            <div
              key={`${image.src}-${index}`}
              ref={el => cardsRef.current[index] = el}
              className="group relative overflow-hidden cursor-pointer card-makonis-glass transition-all duration-500 ease-out hover:-translate-y-2 hover:scale-105 opacity-100"
              style={{
                background: "rgba(255, 255, 255, 0.08)",
                backdropFilter: "blur(15px)",
                border: "1px solid rgba(255, 255, 255, 0.1)",
                borderRadius: "1.5rem",
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
              }}
              onClick={() => {
                setSelectedImage(image.src);
                lockScroll();
              }}>
              <div className="relative h-64 overflow-hidden rounded-t-3xl">
                <img
                  src={image.src}
                  alt={image.alt}
                  loading="lazy"
                  onError={(e) => {
                    e.target.src = "https://via.placeholder.com/400x300/002956/FFFFFF?text=Image+Not+Found";
                  }}
                  className="w-full h-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-110"
                  style={{
                    minHeight: '200px',
                    backgroundColor: '#f0f0f0',
                  }} />
                <div
                  className="absolute inset-0 flex items-end p-4 transition-opacity duration-300 opacity-0 group-hover:opacity-100"
                  style={{
                    background: "linear-gradient(to bottom, rgba(0,0,0,0.0) 0%, rgba(0,0,0,0.3) 40%, rgba(0, 160, 233, 0.8) 100%)"
                  }}>
                  <p className="text-white text-sm font-medium">{image.alt}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center mt-12 space-x-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed card-makonis-glass border border-white/20 text-white hover:border-[#00a0e9]/50"
              style={{
                background: "rgba(255, 255, 255, 0.08)",
                backdropFilter: "blur(15px)",
              }}>
              Previous
            </button>
            {[...Array(totalPages)].map((_, index) => (
              <button
                key={index + 1}
                onClick={() => handlePageChange(index + 1)}
                className={`px-4 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 ${
                  currentPage === index + 1
                    ? 'text-white border-[#00a0e9]'
                    : 'text-white border-white/20 hover:border-[#00a0e9]/50'
                } card-makonis-glass border`}
                style={{
                  background: currentPage === index + 1
                    ? "linear-gradient(135deg, rgba(0, 160, 233, 0.3) 0%, rgba(0, 160, 233, 0.5) 100%)"
                    : "rgba(255, 255, 255, 0.08)",
                  backdropFilter: "blur(15px)",
                }}>
                {index + 1}
              </button>
            ))}
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed card-makonis-glass border border-white/20 text-white hover:border-[#00a0e9]/50"
              style={{
                background: "rgba(255, 255, 255, 0.08)",
                backdropFilter: "blur(15px)",
              }}>
              Next
            </button>
          </div>
        )}
      </div>

      {/* Lightbox modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 z-[9999] flex items-center justify-center p-4"
          style={{
            position: 'fixed',
            top: 0, left: 0, width: '100vw', height: '100vh',
            overflow: 'hidden',
            backgroundColor: 'rgba(0, 0, 0, 0.95)',
            backdropFilter: 'blur(10px)',
          }}
          onClick={() => {
            setSelectedImage(null);
            unlockScroll();
          }}>
            <div className="relative max-w-full max-h-full flex flex-col items-center z-10">
              <img
                src={selectedImage}
                alt="Full screen view"
                className="max-w-full max-h-[80vh] object-contain transition-transform duration-300 hover:scale-110"
                onClick={(e) => e.stopPropagation()}
                onError={e => e.target.src = "https://via.placeholder.com/800x600/002956/FFFFFF?text=Image+Not+Found"}
                style={{maxWidth:'90vw',maxHeight:'80vh',objectFit:'contain'}}
              />
              <p className="text-white mt-4 text-lg font-medium bg-black bg-opacity-50 px-4 py-2 rounded-lg">
                {imageData.find((img) => img.src === selectedImage)?.alt}
              </p>
            </div>
            <button onClick={() => { setSelectedImage(null); unlockScroll(); }}
              className="absolute top-5 right-5 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12"/></svg>
            </button>
            <button onClick={e => { e.stopPropagation(); handlePrev(); }} className="absolute left-5 top-1/2 -translate-y-1/2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7"/></svg>
            </button>
            <button onClick={e => { e.stopPropagation(); handleNext(); }} className="absolute right-5 top-1/2 -translate-y-1/2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"/></svg>
            </button>
        </div>
      )}

      {/* Memories/Slideshow modal, no cross-fade but safe */}
      {isMemoriesPlayerOpen && (
        <div
          className="fixed inset-0 z-[9999] flex flex-col items-center justify-center"
          style={{
            position: 'fixed',
            top: 0, left: 0, width: '100vw', height: '90vh',
            overflow: 'hidden',
            backgroundColor: '#000000',
          }}
          onClick={(e) => {
            // Close if clicking on the background (not on content)
            if (e.target === e.currentTarget) {
              handleCloseMemories();
            }
          }}>
          <div className="absolute inset-0 overflow-hidden bg-black">
            {imageData[currentVideoIndex] && (
              <img
                key={`current-${currentVideoIndex}`}
                src={imageData[currentVideoIndex].src}
                alt={imageData[currentVideoIndex].alt}
                className="w-full h-full object-cover no-zoom"
                style={{
                  opacity: 1,
                  position: 'absolute',
                  top: 0, left: 0,
                  zIndex: 1, width: '100%', height: '100%',
                  objectFit: 'cover',
                }}
                onError={e => e.target.src = "https://via.placeholder.com/1920x1080/002956/FFFFFF?text=Loading+Memories"}
              />
            )}
            <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/50 z-2"></div>
          </div>
          <div className="relative z-10 w-full h-full flex flex-col justify-between p-6">
            {/* UI controls etc, keep your original here */}
            <div className="flex justify-between items-start">
              <div className="card-makonis-glass p-4 rounded-xl" style={{
                background: "rgba(0, 0, 0, 0.6)",
                backdropFilter: "blur(15px)",
                border: "1px solid rgba(255, 255, 255, 0.1)"
              }}>
                <h3 className="font-bold text-xl text-white mb-1">Life @ Makonis: Our Story</h3>
                {currentMusic && (
                  <p className="text-sm text-[#00a0e9] flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12a7.971 7.971 0 00-1.343-4.243 1 1 0 010-1.414z" clipRule="evenodd"/>
                    </svg>
                    {currentMusic.title}
                  </p>
                )}
              </div>
              <button onClick={handleCloseMemories}
                className="p-4 rounded-full text-white hover:text-red-400 transition-all duration-300 hover:scale-110 border-2 border-white/30 hover:border-red-400"
                style={{
                  background: "rgba(0, 0, 0, 0.8)",
                  backdropFilter: "blur(15px)",
                  boxShadow: "0 0 20px rgba(255, 255, 255, 0.3)",
                }}
                title="Close (ESC)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12"/>
                </svg>
              </button>
            </div>
            <div className="text-white text-center card-makonis-glass p-6 rounded-xl self-center max-w-lg" style={{
              background: "rgba(0, 0, 0, 0.6)",
              backdropFilter: "blur(15px)",
              border: "1px solid rgba(255, 255, 255, 0.1)"
            }}>
              <p className="font-medium mb-4 text-lg">{imageData[currentVideoIndex].alt}</p>
              <button
                onClick={togglePlayPause}
                className="inline-flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 card-makonis-glass border border-[#00a0e9]/30 text-white"
                style={{
                  background: "linear-gradient(135deg, rgba(0, 160, 233, 0.2) 0%, rgba(0, 160, 233, 0.4) 100%)",
                  backdropFilter: "blur(15px)",
                }}>
                {isPlaying ? (
                  <>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd"/>
                    </svg>
                    Pause
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd"/>
                    </svg>
                    Play
                  </>
                )}
              </button>
              <div className="text-sm mt-4 text-[#00a0e9] font-medium">
                {currentVideoIndex + 1} of {imageData.length}
              </div>
            </div>
            {/* Progress bar */}
            <div className="w-full">
              <div className="w-full h-2 bg-black/50 rounded-full overflow-hidden">
                <div className="progress-bar h-full rounded-full"
                  style={{
                    width: isPlaying ? "100%" : "0%",
                    animationPlayState: isPlaying ? "running" : "paused"
                  }}></div>
              </div>
              <div className="flex justify-between text-xs text-white/70 mt-2">
                <span>Playing memories slideshow</span>
                <span>{Math.floor((currentVideoIndex / imageData.length) * 100)}% complete</span>
              </div>
            </div>
          </div>
          <audio ref={audioRef} src={currentMusic?.src} loop />
        </div>
      )}
    </div>
    </>
  );
};

export default LifeAtMakonisPage;
